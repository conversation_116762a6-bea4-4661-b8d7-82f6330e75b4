<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1749525603755" clover="3.2.0">
  <project timestamp="1749525603756" name="All files">
    <metrics statements="558" coveredstatements="0" conditionals="122" coveredconditionals="0" methods="108" coveredmethods="0" elements="788" coveredelements="0" complexity="0" loc="558" ncloc="558" packages="5" files="7" classes="7"/>
    <package name="src">
      <metrics statements="378" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="51" coveredmethods="0"/>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts">
        <metrics statements="378" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="51" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="168" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="235" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="240" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="282" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="288" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="314" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="315" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="361" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="362" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="417" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="456" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="457" count="0" type="stmt"/>
        <line num="458" count="0" type="stmt"/>
        <line num="461" count="0" type="stmt"/>
        <line num="463" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="464" count="0" type="stmt"/>
        <line num="466" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="480" count="0" type="stmt"/>
        <line num="482" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="483" count="0" type="stmt"/>
        <line num="484" count="0" type="stmt"/>
        <line num="491" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="501" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="514" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="515" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="518" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="521" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="525" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="545" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="564" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="576" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="587" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="589" count="0" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="597" count="0" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="625" count="0" type="stmt"/>
        <line num="626" count="0" type="stmt"/>
        <line num="627" count="0" type="stmt"/>
        <line num="630" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="642" count="0" type="stmt"/>
        <line num="646" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="650" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="651" count="0" type="stmt"/>
        <line num="652" count="0" type="stmt"/>
        <line num="656" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="667" count="0" type="stmt"/>
        <line num="668" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="673" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
        <line num="676" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="677" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="686" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="687" count="0" type="stmt"/>
        <line num="688" count="0" type="stmt"/>
        <line num="692" count="0" type="stmt"/>
        <line num="693" count="0" type="stmt"/>
        <line num="694" count="0" type="stmt"/>
        <line num="695" count="0" type="stmt"/>
        <line num="696" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="700" count="0" type="stmt"/>
        <line num="703" count="0" type="stmt"/>
        <line num="710" count="0" type="stmt"/>
        <line num="711" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="716" count="0" type="stmt"/>
        <line num="717" count="0" type="stmt"/>
        <line num="719" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="720" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="725" count="0" type="stmt"/>
        <line num="726" count="0" type="stmt"/>
        <line num="728" count="0" type="stmt"/>
        <line num="733" count="0" type="stmt"/>
        <line num="734" count="0" type="stmt"/>
        <line num="739" count="0" type="stmt"/>
        <line num="740" count="0" type="stmt"/>
        <line num="743" count="0" type="stmt"/>
        <line num="744" count="0" type="stmt"/>
        <line num="745" count="0" type="stmt"/>
        <line num="748" count="0" type="stmt"/>
        <line num="749" count="0" type="stmt"/>
        <line num="750" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="752" count="0" type="stmt"/>
        <line num="759" count="0" type="stmt"/>
        <line num="762" count="0" type="stmt"/>
        <line num="763" count="0" type="stmt"/>
        <line num="764" count="0" type="stmt"/>
        <line num="794" count="0" type="stmt"/>
        <line num="797" count="0" type="stmt"/>
        <line num="798" count="0" type="stmt"/>
        <line num="824" count="0" type="stmt"/>
        <line num="827" count="0" type="stmt"/>
        <line num="828" count="0" type="stmt"/>
        <line num="848" count="0" type="stmt"/>
        <line num="851" count="0" type="stmt"/>
        <line num="852" count="0" type="stmt"/>
        <line num="858" count="0" type="stmt"/>
        <line num="862" count="0" type="stmt"/>
        <line num="863" count="0" type="stmt"/>
        <line num="864" count="0" type="stmt"/>
        <line num="911" count="0" type="stmt"/>
        <line num="914" count="0" type="stmt"/>
        <line num="915" count="0" type="stmt"/>
        <line num="918" count="0" type="stmt"/>
        <line num="919" count="0" type="stmt"/>
        <line num="920" count="0" type="stmt"/>
        <line num="923" count="0" type="stmt"/>
        <line num="924" count="0" type="stmt"/>
        <line num="925" count="0" type="stmt"/>
        <line num="927" count="0" type="stmt"/>
        <line num="933" count="0" type="stmt"/>
        <line num="937" count="0" type="stmt"/>
        <line num="938" count="0" type="stmt"/>
        <line num="939" count="0" type="stmt"/>
        <line num="1013" count="0" type="stmt"/>
        <line num="1016" count="0" type="stmt"/>
        <line num="1017" count="0" type="stmt"/>
        <line num="1018" count="0" type="stmt"/>
        <line num="1024" count="0" type="stmt"/>
        <line num="1027" count="0" type="stmt"/>
        <line num="1028" count="0" type="stmt"/>
        <line num="1029" count="0" type="stmt"/>
        <line num="1036" count="0" type="stmt"/>
        <line num="1040" count="0" type="stmt"/>
        <line num="1041" count="0" type="stmt"/>
        <line num="1042" count="0" type="stmt"/>
        <line num="1121" count="0" type="stmt"/>
        <line num="1124" count="0" type="stmt"/>
        <line num="1125" count="0" type="stmt"/>
        <line num="1126" count="0" type="stmt"/>
        <line num="1132" count="0" type="stmt"/>
        <line num="1136" count="0" type="stmt"/>
        <line num="1137" count="0" type="stmt"/>
        <line num="1138" count="0" type="stmt"/>
        <line num="1190" count="0" type="stmt"/>
        <line num="1193" count="0" type="stmt"/>
        <line num="1194" count="0" type="stmt"/>
        <line num="1195" count="0" type="stmt"/>
        <line num="1201" count="0" type="stmt"/>
        <line num="1205" count="0" type="stmt"/>
        <line num="1206" count="0" type="stmt"/>
        <line num="1207" count="0" type="stmt"/>
        <line num="1269" count="0" type="stmt"/>
        <line num="1272" count="0" type="stmt"/>
        <line num="1273" count="0" type="stmt"/>
        <line num="1274" count="0" type="stmt"/>
        <line num="1280" count="0" type="stmt"/>
        <line num="1283" count="0" type="stmt"/>
        <line num="1284" count="0" type="stmt"/>
        <line num="1285" count="0" type="stmt"/>
        <line num="1291" count="0" type="stmt"/>
        <line num="1295" count="0" type="stmt"/>
        <line num="1296" count="0" type="stmt"/>
        <line num="1297" count="0" type="stmt"/>
        <line num="1308" count="0" type="stmt"/>
        <line num="1311" count="0" type="stmt"/>
        <line num="1312" count="0" type="stmt"/>
        <line num="1313" count="0" type="stmt"/>
        <line num="1319" count="0" type="stmt"/>
        <line num="1323" count="0" type="stmt"/>
        <line num="1324" count="0" type="stmt"/>
        <line num="1325" count="0" type="stmt"/>
        <line num="1326" count="0" type="stmt"/>
        <line num="1329" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="1331" count="0" type="stmt"/>
        <line num="1352" count="0" type="stmt"/>
        <line num="1355" count="0" type="stmt"/>
        <line num="1356" count="0" type="stmt"/>
        <line num="1357" count="0" type="stmt"/>
        <line num="1373" count="0" type="stmt"/>
        <line num="1377" count="0" type="stmt"/>
        <line num="1378" count="0" type="stmt"/>
        <line num="1386" count="0" type="stmt"/>
        <line num="1389" count="0" type="stmt"/>
        <line num="1390" count="0" type="stmt"/>
        <line num="1391" count="0" type="stmt"/>
        <line num="1392" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="1393" count="0" type="stmt"/>
        <line num="1394" count="0" type="stmt"/>
        <line num="1395" count="0" type="stmt"/>
        <line num="1399" count="0" type="stmt"/>
        <line num="1400" count="0" type="stmt"/>
        <line num="1403" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1404" count="0" type="stmt"/>
        <line num="1405" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="1406" count="0" type="stmt"/>
        <line num="1410" count="0" type="stmt"/>
        <line num="1422" count="0" type="stmt"/>
        <line num="1427" count="0" type="stmt"/>
        <line num="1429" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="1430" count="0" type="stmt"/>
        <line num="1431" count="0" type="stmt"/>
        <line num="1432" count="0" type="stmt"/>
        <line num="1436" count="0" type="stmt"/>
      </file>
      <file name="vite-env.d.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.ui">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="use-toast.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="84" coveredstatements="0" conditionals="29" coveredconditionals="0" methods="32" coveredmethods="0"/>
      <file name="use-toast.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts">
        <metrics statements="53" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="164" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
      <file name="useComplianceApi.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts">
        <metrics statements="31" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="utils.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="90" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="22" coveredmethods="0"/>
      <file name="complianceApi.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts">
        <metrics statements="90" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="269" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="289" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="311" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="321" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="332" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="342" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="353" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="362" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="363" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="372" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="373" count="0" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
